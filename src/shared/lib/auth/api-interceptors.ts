import api from '../api/api';
import { handleAuthError } from './handle-auth-error';

/**
 * Configura os interceptors da API para autenticação
 * Esta função deve ser chamada após a inicialização da aplicação
 * para evitar dependências circulares
 * @returns ID do interceptor configurado
 */
export const setupApiInterceptors = (): number => {
	// Configura o interceptor de resposta para lidar com erros de autenticação
	return api.interceptors.response.use((response) => response, handleAuthError);
};

/**
 * Remove todos os interceptors de resposta da API
 * Útil para testes ou quando necessário limpar os interceptors
 */
export const clearApiInterceptors = (): void => {
	api.interceptors.response.clear();
};
