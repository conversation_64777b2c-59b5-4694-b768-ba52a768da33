import { getUserInitials, getUserRoles, userState } from '@/contexts/auth/states/user.state';
import { Avatar, Image, Navbar as Nav, NavbarContent, NavbarItem, NavbarMenu, NavbarMenuItem, NavbarMenuToggle } from '@nextui-org/react';
import { useAtomValue } from 'jotai';
import { ChevronDown, HelpCircle, LogOut, Settings, Shield, User } from 'lucide-react';
import { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from './dropdown-menu';

export function Navbar() {
	const [isMenuOpen, setIsMenuOpen] = useState(false);
	const user = useAtomValue(userState);
	const userInitials = useAtomValue(getUserInitials);
	const userRoles = useAtomValue(getUserRoles);

	const pages = [
		{ to: '/devices', label: 'Dispositivos', id: 'devices' },
		{ to: '/presentations', label: 'Apresentações', id: 'presentations' },
		{ to: '/programacao', label: 'Programação', id: 'programming' },
	];

	const handleLogout = () => {
		console.log('Logout clicked');
	};

	const closeMenu = () => {
		setIsMenuOpen(false);
	};

	return (
		<Nav maxWidth="2xl" className="bg-muted px-2 py-2 md:px-4" isBordered isMenuOpen={isMenuOpen} onMenuOpenChange={setIsMenuOpen}>
			<NavbarContent justify="start" className="flex gap-6 md:gap-6">
				<NavbarMenuToggle className="text-white md:hidden" />
				<NavLink to="/home" className="group flex w-full flex-1 items-center justify-center md:justify-start md:gap-3">
					<Image
						className="max-h-[36px] max-w-[36px] rounded-none object-contain drop-shadow-lg md:max-h-[40px] md:max-w-[40px]"
						src="/assets/svgs/reduced-logo.svg"
						width={36}
						height={36}
						alt="StreamHub Logo"
					/>
					<div className="flex flex-col items-center justify-center px-2 py-1">
						<span className="whitespace-nowrap text-xl font-extrabold tracking-tight text-white transition-colors group-hover:text-green-400 md:text-2xl">
							Stream <span className="text-green-400 transition-colors group-hover:text-white">Hub</span>
						</span>
						<span className="-mt-1 whitespace-nowrap text-xs font-semibold tracking-widest text-white group-hover:text-green-400">
							Pormade
						</span>
					</div>
				</NavLink>
				{pages.map((page) => (
					<NavbarItem key={page.id} className="hidden md:block">
						<NavLink
							to={page.to}
							className="rounded-md px-3 py-1 font-medium text-white transition-all duration-200 hover:bg-green-500/10 hover:text-green-400"
							style={({ isActive }) => ({
								color: isActive ? '#4ade80' : undefined,
								fontWeight: isActive ? 'bold' : undefined,
								background: isActive ? 'rgba(74, 222, 128, 0.1)' : undefined,
								boxShadow: isActive ? '0 0 0 1px rgba(74, 222, 128, 0.3)' : undefined,
							})}
						>
							{page.label}
						</NavLink>
					</NavbarItem>
				))}
			</NavbarContent>

			<NavbarContent justify="end">
				<NavbarItem>
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<div className="flex cursor-pointer items-center gap-2 rounded-md px-1 py-1 transition hover:bg-gray-800/50 md:gap-3 md:px-2">
								<div className="hidden flex-col items-end md:flex">
									<span className="text-md font-semibold text-white">{user?.name}</span>
									<span className="text-xs text-gray-400">{user?.email}</span>
								</div>
								<div className="relative">
									<Avatar
										size="md"
										name={userInitials}
										className="ring-2 ring-green-400 ring-offset-1 ring-offset-[#121214] transition-transform hover:scale-105"
									/>
									<ChevronDown size={12} className="absolute -bottom-1 -right-1 text-green-400" />
								</div>
							</div>
						</DropdownMenuTrigger>
						<DropdownMenuContent
							align="end"
							className="w-80 border border-gray-800/60 bg-gradient-to-br from-[#1c1c20] via-[#121214] to-[#1a1a1d] p-0 shadow-xl shadow-green-500/5"
						>
							{/* Header do usuário */}
							<div className="border-b border-gray-800/50 bg-gradient-to-r from-green-500/10 to-transparent p-4">
								<div className="flex items-center gap-3">
									<Avatar size="lg" name={userInitials} className="ring-2 ring-green-400 ring-offset-1 ring-offset-[#121214]" />
									<div className="flex-1">
										<h3 className="font-semibold text-white">{user?.name}</h3>
										<p className="text-sm text-gray-400">{user?.email}</p>
										<div className="mt-1 flex items-center gap-2">
											<div className="h-2 w-2 rounded-full bg-green-400"></div>
											<span className="text-xs text-green-400">Online</span>
											{userRoles.length > 0 && (
												<>
													<span className="text-xs text-gray-500">•</span>
													<div className="flex items-center gap-1">
														<Shield size={12} className="text-blue-400" />
														<span className="text-xs text-blue-400">
															{userRoles.length} role{userRoles.length > 1 ? 's' : ''}
														</span>
													</div>
												</>
											)}
										</div>
									</div>
								</div>
							</div>

							{/* Menu items */}
							<div className="p-1">
								<DropdownMenuItem className="flex items-center gap-3 rounded-md px-3 py-2.5 text-white transition-colors hover:bg-green-400/10 hover:text-green-400 focus:bg-green-400/10 focus:text-green-400">
									<User size={16} className="text-green-400" />
									<span>Meu Perfil</span>
								</DropdownMenuItem>

								<DropdownMenuItem className="flex items-center gap-3 rounded-md px-3 py-2.5 text-white transition-colors hover:bg-blue-400/10 hover:text-blue-400 focus:bg-blue-400/10 focus:text-blue-400">
									<Settings size={16} className="text-blue-400" />
									<span>Configurações</span>
								</DropdownMenuItem>

								<DropdownMenuItem className="flex items-center gap-3 rounded-md px-3 py-2.5 text-white transition-colors hover:bg-purple-400/10 hover:text-purple-400 focus:bg-purple-400/10 focus:text-purple-400">
									<HelpCircle size={16} className="text-purple-400" />
									<span>Ajuda & Suporte</span>
								</DropdownMenuItem>

								<DropdownMenuSeparator className="my-1 bg-gray-800/50" />

								<DropdownMenuItem
									onClick={handleLogout}
									className="flex items-center gap-3 rounded-md px-3 py-2.5 text-red-400 transition-colors hover:bg-red-400/10 hover:text-red-300 focus:bg-red-400/10 focus:text-red-300"
								>
									<LogOut size={16} />
									<span>Sair</span>
								</DropdownMenuItem>
							</div>
						</DropdownMenuContent>
					</DropdownMenu>
				</NavbarItem>
			</NavbarContent>

			<NavbarMenu className="border-t border-gray-800 bg-muted bg-gradient-to-b from-background/50 to-background/95 p-5">
				{pages.map((page) => (
					<NavbarMenuItem key={page.id}>
						<NavLink
							to={page.to}
							onClick={closeMenu}
							className={({ isActive }) =>
								`block rounded-md px-4 py-3 text-base transition-colors ${
									isActive
										? 'bg-green-500/10 font-bold text-green-400 shadow-[0_0_0_1px_rgba(74,222,128,0.3)]'
										: 'text-white hover:bg-green-500/10 hover:text-green-400'
								}`
							}
						>
							{page.label}
						</NavLink>
					</NavbarMenuItem>
				))}
			</NavbarMenu>
		</Nav>
	);
}
