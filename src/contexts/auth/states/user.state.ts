import { CurrentUser } from '@/shared/interfaces/users/current-user';
import { atom } from 'jotai';

export const userState = atom<CurrentUser | null>(null);

export const getUserInitials = atom((get) => {
	const user = get(userState);
	if (!user) return '';
	const words = user.name.split(' ').filter((word) => word.length > 0);
	if (words.length === 0) return '';
	if (words.length === 1) return words[0].charAt(0).toUpperCase();
	const firstInitial = words[0].charAt(0).toUpperCase();
	const lastInitial = words[words.length - 1].charAt(0).toUpperCase();

	return firstInitial + lastInitial;
});

export const getUserRoles = atom((get) => {
	const user = get(userState);
	if (!user) return [];
	return user.roles || [];
});
